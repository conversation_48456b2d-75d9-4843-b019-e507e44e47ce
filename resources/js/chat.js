/**
 * Chat functionality for Laravel Reverb
 *
 * This file contains all JavaScript functionality for the chat interface.
 */

// Typing indicator management
class TypingIndicator {
    constructor() {
        this.typingTimeout = null;
        this.isCurrentlyTyping = false;
        this.typingTimeoutDuration = 3000; // 3 seconds
        this.debounceDelay = 500; // 500ms debounce
        this.lastTypingTime = 0;

        this.init();
    }

    init() {
        this.bindEvents();
        this.setupEchoListeners();
    }

    bindEvents() {
        const messageInput = document.getElementById('message-input');
        if (!messageInput) return;

        // Handle typing detection with debouncing
        messageInput.addEventListener('input', (e) => {
            this.handleTyping();
        });

        // Stop typing on Enter key (form submission)
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.stopTyping();
            }
        });

        // Stop typing when input loses focus
        messageInput.addEventListener('blur', () => {
            this.stopTyping();
        });
    }

    handleTyping() {
        const now = Date.now();

        // Debounce rapid keystrokes
        if (now - this.lastTypingTime < this.debounceDelay) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = setTimeout(() => this.stopTyping(), this.typingTimeoutDuration);
            return;
        }

        this.lastTypingTime = now;

        // Only send typing event if not already typing
        if (!this.isCurrentlyTyping) {
            this.startTyping();
        }

        // Reset the timeout
        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => this.stopTyping(), this.typingTimeoutDuration);
    }

    startTyping() {
        if (this.isCurrentlyTyping) return;

        this.isCurrentlyTyping = true;
        console.log('Starting typing...');

        // Call Livewire method to broadcast typing event
        if (window.Livewire) {
            window.Livewire.dispatch('userTyping');
        }
    }

    stopTyping() {
        if (!this.isCurrentlyTyping) return;

        this.isCurrentlyTyping = false;
        clearTimeout(this.typingTimeout);
        console.log('Stopping typing...');

        // Call Livewire method to broadcast stop typing event
        if (window.Livewire) {
            window.Livewire.dispatch('userStoppedTyping');
        }
    }

    setupEchoListeners() {
        // Listen for messages - use userId because we want to receive messages sent TO us
        window.Echo.private('chat-channel.' + window.userId)
            .listen('MessageSentEvent', (event) => {
                console.log('Message received:', event);
                if (window.Livewire) {
                    window.Livewire.dispatch('chatUpdated');
                }
                this.scrollToBottom();
            });

        // Listen for typing events - use userId because we want to receive typing events sent TO us
        window.Echo.private('chat-channel.' + window.userId)
            .listen('UserTypingEvent', (event) => {
                console.log('Typing event received:', event);
                this.handleRemoteTyping(event);
            });
    }

    handleRemoteTyping(event) {
        console.log('Processing typing event:', event);

        // Don't show typing indicator for our own typing
        if (event.sender_id === window.userId) {
            console.log('Ignoring own typing event');
            return;
        }

        if (event.is_typing) {
            console.log('Showing typing indicator');
            this.showTypingIndicator();
        } else {
            console.log('Hiding typing indicator');
            this.hideTypingIndicator();
        }
    }

    showTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (!indicator) {
            console.error('Typing indicator element not found');
            return;
        }

        console.log('Making typing indicator visible');
        indicator.classList.remove('hidden');
        // Force reflow before adding opacity
        indicator.offsetHeight;
        indicator.classList.remove('opacity-0');
        indicator.classList.add('opacity-100');

        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (!indicator) {
            console.error('Typing indicator element not found');
            return;
        }

        console.log('Hiding typing indicator');
        indicator.classList.remove('opacity-100');
        indicator.classList.add('opacity-0');

        // Hide completely after transition
        setTimeout(() => {
            indicator.classList.add('hidden');
        }, 300);
    }

    scrollToBottom() {
        setTimeout(() => {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 50);
    }
}

// Handle file selection for attachments
window.handleFileSelect = function(fileInput) {
    if (fileInput.files && fileInput.files[0]) {
        const fileName = fileInput.files[0].name;
        document.getElementById('message-input').value = 'File: ' + fileName;
        // Focus the input after file selection
        document.getElementById('message-input').focus();
    }
};

// Legacy scroll function for compatibility
function scrollToBottom() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Initialize typing indicator when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing typing indicator...');
    window.typingIndicator = new TypingIndicator();
});

// Scroll to the bottom of the chat messages container
if (window.Livewire) {
    window.Livewire.on('chatUpdated', () => {
        setTimeout(scrollToBottom, 50);
    });
}

window.onload = () => {
    scrollToBottom();
};

// Debug function to test typing indicator manually
window.testTypingIndicator = function() {
    console.log('Testing typing indicator...');
    const indicator = document.getElementById('typing-indicator');
    if (indicator) {
        console.log('Found typing indicator element');
        indicator.classList.remove('hidden');
        indicator.offsetHeight; // Force reflow
        indicator.classList.remove('opacity-0');
        indicator.classList.add('opacity-100');
        console.log('Typing indicator should now be visible');

        // Hide after 3 seconds
        setTimeout(() => {
            indicator.classList.remove('opacity-100');
            indicator.classList.add('opacity-0');
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, 300);
        }, 3000);
    } else {
        console.error('Typing indicator element not found!');
    }
};
