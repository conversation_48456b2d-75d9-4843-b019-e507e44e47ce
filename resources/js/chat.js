/**
 * Chat functionality for <PERSON><PERSON> Reverb
 *
 * This file contains all JavaScript functionality for the chat interface.
 */

// Handle file selection for attachments
window.handleFileSelect = function(fileInput) {
    if (fileInput.files && fileInput.files[0]) {
        const fileName = fileInput.files[0].name;
        document.getElementById('message-input').value = 'File: ' + fileName;
        // Focus the input after file selection
        document.getElementById('message-input').focus();
    }
};

window.Echo.private('chat-channel.' + userId)
    .listen('MessageSentEvent', (event) => {
        Livewire.emit('chatUpdated');
    });

window.Echo.private(`chat-channel.${senderId}`)
    .listen('UserTypingEvent', (event) => {
        console.log(event);
    });

function scrollToBottom() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    window.scrollTo(0, chatMessages.scrollHeight);
}

// Scroll to the bottom of the chat messages container
Livewire.on('chatUpdated', () => {
    setTimeout(scrollToBottom, 50);
});

window.onload =() => {
    scrollToBottom();
}
