<div class="h-full flex flex-col">
    <x-slot name="header">
        <div class="flex items-center space-x-3">
            <div
                class="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-md">
                {{ substr($user->name, 0, 1) }}
            </div>
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $user->name }}
                </h2>
                <div class="flex items-center text-xs text-gray-500">
                    <span class="inline-flex h-2 w-2 rounded-full bg-green-400 mr-2"></span>
                    Active now
                </div>
            </div>
        </div>
    </x-slot>

    <div class="py-4 flex-1 flex flex-col h-[calc(100vh-10rem)]">
        <div class="max-w-6xl w-full mx-auto sm:px-6 lg:px-8 flex-1 flex flex-col">
            <div class="bg-white overflow-hidden shadow-md sm:rounded-lg flex flex-col flex-1">
                <!-- Chat messages container -->
                <div id="chat-messages"
                     class="flex-1 overflow-y-auto p-6 bg-gray-50 flex flex-col space-y-4 min-h-[500px]">
                    <!-- Date separator -->
                    <div class="flex justify-center my-2 sticky top-0 z-10">
                        <span class="px-4 py-1 rounded-full bg-gray-200 text-xs font-medium text-gray-700 shadow-sm">Today</span>
                    </div>

                    @foreach($this->messages as $message)
                        @if($message->sender_id == auth()->id())
                            <!-- Sent message -->
                            <div class="flex items-start justify-end group">
                                <div class="max-w-[75%] transform transition-transform duration-200 hover:scale-[1.01]">
                                    <div
                                        class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl rounded-tr-none p-4 shadow-sm">
                                        <p class="text-white text-base">{{ $message->content }}</p>
                                    </div>
                                    <div
                                        class="mt-1 text-xs text-gray-500 text-right flex items-center justify-end space-x-1">
                                        <span>{{ $message->created_at ? $message->created_at->format('h:i A') : 'N/A' }}</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-blue-500"
                                             viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        @else
                            <!-- Received message -->
                            <div class="flex items-start space-x-3 max-w-[75%] group">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold flex-shrink-0 shadow-sm">
                                    {{ substr($user->name, 0, 1) }}
                                </div>
                                <div class="transform transition-transform duration-200 hover:scale-[1.01]">
                                    <div
                                        class="bg-white rounded-2xl rounded-tl-none p-4 shadow-sm border border-gray-100">
                                        <p class="text-gray-800 text-base">{{ $message->content }}</p>
                                    </div>
                                    <div class="mt-1 text-xs text-gray-500">
                                        {{ $message->created_at ? $message->created_at->format('h:i A') : 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach

                    <!-- Typing Indicator -->
                    <div id="typing-indicator" class="hidden flex items-start space-x-3 max-w-[75%] group opacity-0 transition-opacity duration-300">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold flex-shrink-0 shadow-sm">
                            {{ substr($user->name, 0, 1) }}
                        </div>
                        <div class="transform transition-transform duration-200">
                            <div class="bg-white rounded-2xl rounded-tl-none p-4 shadow-sm border border-gray-100">
                                <div class="flex items-center space-x-1">
                                    <span id="typing-text" class="text-gray-600 text-sm">{{ $user->name }} is typing</span>
                                    <div class="flex space-x-1 ml-2">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Message input -->
                <div class="p-4 border-t border-gray-100 bg-white">
                    <form wire:submit.prevent="sendMessage" class="flex items-center space-x-3">
                        <div class="relative flex-1">
                            <input wire:keydown="userTyping" wire:model="message" type="text" id="message-input"
                                   class="w-full px-5 py-4 border border-gray-200 rounded-full focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none bg-gray-50 placeholder-gray-400 pr-12 text-base"
                                   placeholder="Type your message..."/>
                            <input type="file" id="file-upload" class="hidden" onchange="handleFileSelect(this)"/>
                            <button type="button" onclick="document.getElementById('file-upload').click()"
                                    class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-500 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                     stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                                </svg>
                            </button>
                        </div>
                        <button type="submit"
                                class="inline-flex items-center px-6 py-4 bg-gradient-to-r from-indigo-500 to-purple-600 border border-transparent rounded-full font-medium text-sm text-white tracking-wide hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20"
                                 fill="currentColor">
                                <path
                                    d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


@push('scripts')
    <script src="{{ asset('js/app.js') }}" type="module"></script>
@endpush

@push('styles')
<style>
    /* Enhanced typing indicator animations */
    @keyframes typing-bounce {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-10px);
        }
    }

    .typing-dot {
        animation: typing-bounce 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: 0ms; }
    .typing-dot:nth-child(2) { animation-delay: 160ms; }
    .typing-dot:nth-child(3) { animation-delay: 320ms; }

    /* Smooth transitions for typing indicator */
    #typing-indicator {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    }

    #typing-indicator.opacity-100 {
        transform: translateY(0);
    }

    #typing-indicator.opacity-0 {
        transform: translateY(10px);
    }
</style>
@endpush

