<?php

namespace App\Livewire;

use App\Events\MessageSentEvent;
use App\Events\UserTypingEvent;
use App\Models\Message;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\View;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Models\User;

class Chat extends Component
{
    public int $userId;
    public string $message = '';
    public int $senderId;
    public int $receiverId;
    public Collection $messages;

    public function mount($userId): void
    {
        $this->userId = $userId;
        $this->senderId = auth()->user()->id;
        $this->receiverId = $userId;
        $this->messages = $this->getMessages();
        $this->dispatch('chatUpdated');
    }

    public function render(): View
    {
        return view('livewire.chat', ['user' => User::find($this->userId)]);
    }

    /**
     * Get the messages between the current user and the user they are chatting with.
     *
     * @return Collection
     */
    public function getMessages(): Collection
    {
        return Message::with(['sender:id,name', 'receiver:id,name'])
            ->where(function ($query) {
                $query->where('sender_id', $this->senderId)
                    ->where('receiver_id', $this->receiverId);
            })
            ->orWhere(function ($query) {
                $query->where('sender_id', $this->receiverId)
                    ->where('receiver_id', $this->senderId);
            })
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Send a message to the user.
     *
     * @return void
     */
    public function sendMessage(): void
    {
        // Stop typing indicator when sending message
        $this->userStoppedTyping();

        $sentMessage = $this->saveMessage();
        $this->messages->push($sentMessage);
        broadcast(new MessageSentEvent($sentMessage));
        $this->message = '';
        $this->dispatch('chatUpdated');
    }

    #[On('echo-private:chat-channel.{senderId},MessageSentEvent')]
    public function receiveMessage($event): void
    {
        $this->messages = $this->getMessages();
        $this->dispatch('chatUpdated');
    }
    public function userTyping(): void
    {
        broadcast(new UserTypingEvent($this->senderId, $this->receiverId, true))->toOthers();
    }

    public function userStoppedTyping(): void
    {
        broadcast(new UserTypingEvent($this->senderId, $this->receiverId, false))->toOthers();
    }

    #[On('userTyping')]
    public function handleUserTyping(): void
    {
        $this->userTyping();
    }

    #[On('userStoppedTyping')]
    public function handleUserStoppedTyping(): void
    {
        $this->userStoppedTyping();
    }

    /**
     * Save the message to the database.
     *
     * @return Message
     */
    public function saveMessage(): Message
    {
        return Message::create([
            'sender_id' => $this->senderId,
            'receiver_id' => $this->receiverId,
            'content' => $this->message,
        ]);
    }
}
